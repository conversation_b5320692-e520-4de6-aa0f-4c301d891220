"""
Performance Analysis Module for Tower Defense Adaptive AI

Contains PerformanceData class and performance analysis utilities.
"""

import json
import os
from dataclasses import dataclass
from typing import Dict, Any, List, Optional
from datetime import datetime
import math


@dataclass
class PerformanceData:
    """Performance data structure for analysis"""
    win_flag: bool
    lives_remaining: int
    starting_lives: int
    towers_built: Dict[str, int]
    tower_diversity: int
    wave_reached: int = 1
    final_wave: int = 1
    previous_config: Optional[Dict[str, Any]] = None
    previous_config_details: Optional[Dict[str, Any]] = None  # New field for extracted config details
    multi_game_context: Optional[Dict[str, Any]] = None  # For multi-game analysis context
    
    # Enemy Buff System Performance Metrics
    buff_encounters: Optional[Dict[str, int]] = None  # Count of each buff type encountered
    buff_combinations_seen: Optional[List[str]] = None  # List of buff combinations encountered
    most_challenging_buffs: Optional[List[str]] = None  # Buffs that caused most difficulty
    tower_counter_effectiveness: Optional[Dict[str, float]] = None  # How well towers countered specific buffs
    buff_adaptation_score: float = 0.0  # How well player adapted to buffed enemies (0-100)
    enemy_buff_intensity: str = "none"  # none, low, medium, high, extreme
    buff_related_deaths: int = 0  # Lives lost specifically to buffed enemies
    effective_counter_strategies: Optional[List[str]] = None  # Successful strategies against buffs
    
    @property
    def survival_rate(self) -> float:
        """Calculate survival rate as percentage"""
        if self.starting_lives > 0:
            return (self.lives_remaining / self.starting_lives) * 100
        return 0.0
    
    @property
    def score(self) -> float:
        """Calculate overall performance score (0-100)"""
        if self.win_flag:
            # Base win score of 80, plus survival bonus
            base_score = 80
            survival_bonus = min(20, self.survival_rate * 0.2)  # Up to 20 points for survival
            return min(100, base_score + survival_bonus)
        else:
            # Loss scoring based on wave progression and survival
            wave_score = min(60, self.wave_reached * 2)  # Up to 60 points for waves
            survival_score = min(30, self.survival_rate * 0.3)  # Up to 30 points for survival
            diversity_score = min(10, self.tower_diversity * 2)  # Up to 10 points for diversity
            return wave_score + survival_score + diversity_score
    
    @property
    def difficulty_adjusted_score(self) -> float:
        """Score adjusted for game difficulty"""
        return self.score  # Base score without resource adjustments
    
    def get_performance_summary(self) -> str:
        """Get a comprehensive performance summary for AI analysis"""
        outcome = "WON" if self.win_flag else "LOST"
        survival_pct = self.survival_rate
        
        # Extract difficulty from previous config if available
        difficulty = "Unknown"
        if self.previous_config_details:
            difficulty = self.previous_config_details.get('difficulty', 'Unknown')
        elif self.previous_config:
            # Try to extract difficulty from various locations in config
            if '_generation_metadata' in self.previous_config:
                difficulty = self.previous_config['_generation_metadata'].get('difficulty', 'Unknown')
            elif '_adaptive_metadata' in self.previous_config:
                ai_adj = self.previous_config['_adaptive_metadata'].get('ai_adjustments', {})
                if 'difficulty_adjustment' in ai_adj:
                    difficulty = ai_adj['difficulty_adjustment'].get('new_difficulty', 'Unknown')
            elif 'calculated_difficulty' in self.previous_config:
                difficulty = self.previous_config['calculated_difficulty'].get('score', 'Unknown')
        
        tower_usage = f"{self.tower_diversity} types built"
        if self.towers_built:
            total_towers = sum(self.towers_built.values())
            tower_usage += f", {total_towers} total towers"
        
        # Buff system summary
        buff_summary = ""
        if self.buff_encounters:
            total_buffed = sum(self.buff_encounters.values())
            buff_summary = f"\nBuff Encounters: {total_buffed} buffed enemies, {len(self.buff_encounters)} buff types"
            if self.buff_combinations_seen:
                buff_summary += f", {len(self.buff_combinations_seen)} combinations"
            buff_summary += f"\nBuff Intensity: {self.enemy_buff_intensity.title()}"
            if self.buff_adaptation_score > 0:
                buff_summary += f", Adaptation Score: {self.buff_adaptation_score:.1f}/100"
            if self.buff_related_deaths > 0:
                buff_summary += f", Buff Deaths: {self.buff_related_deaths}"
        
        return f"""
GAME OUTCOME: {outcome}
Score: {self.score:.1f}/100
Survival Rate: {survival_pct:.1f}% ({self.lives_remaining}/{self.starting_lives} lives)
Wave Progress: {self.wave_reached}/{self.final_wave}
Tower Strategy: {tower_usage}
Previous Difficulty: {difficulty}{buff_summary}
Performance Level: {'Excellent' if self.score >= 80 else 'Good' if self.score >= 60 else 'Average' if self.score >= 40 else 'Poor'}
"""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for analysis"""
        return {
            'win_flag': self.win_flag,
            'lives_remaining': self.lives_remaining, 
            'starting_lives': self.starting_lives,
            'towers_built': self.towers_built,
            'tower_diversity': self.tower_diversity,
            'wave_reached': self.wave_reached,
            'final_wave': self.final_wave,
            'survival_rate': self.survival_rate,
            'score': self.score,
            'difficulty_adjusted_score': self.difficulty_adjusted_score,
            'previous_config_details': self.previous_config_details,
            # Buff system metrics
            'buff_encounters': self.buff_encounters,
            'buff_combinations_seen': self.buff_combinations_seen,
            'most_challenging_buffs': self.most_challenging_buffs,
            'tower_counter_effectiveness': self.tower_counter_effectiveness,
            'buff_adaptation_score': self.buff_adaptation_score,
            'enemy_buff_intensity': self.enemy_buff_intensity,
            'buff_related_deaths': self.buff_related_deaths,
            'effective_counter_strategies': self.effective_counter_strategies
        }


def list_performance_files() -> List[str]:
    """List available performance data files - Circular buffer of max 5 files"""
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    performance_dir = os.path.join(script_dir, "performance_data")
    
    if not os.path.exists(performance_dir):
        return []
    
    # Look for the 5 circular buffer files (1.json through 5.json)
    existing_files = []
    for i in range(1, 6):
        filename = f"{i}.json"
        filepath = os.path.join(performance_dir, filename)
        if os.path.exists(filepath):
            mod_time = os.path.getmtime(filepath)
            existing_files.append((filename, mod_time))
    
    # Sort by modification time (most recent first)
    existing_files.sort(key=lambda x: x[1], reverse=True)
    
    return [filename for filename, _ in existing_files]


def load_all_recent_performances() -> List[PerformanceData]:
    """Load all available performance files from the circular buffer"""
    performances = []
    files = list_performance_files()
    
    print(f"Loading {len(files)} recent performance files...")
    for filename in files:
        try:
            performance = load_performance_from_file(filename)
            if performance:
                performances.append(performance)
                print(f"   ✓ Loaded {filename} (Score: {performance.score:.1f})")
        except Exception as e:
            print(f"   ✗ Failed to load {filename}: {e}")
    
    return performances


def load_performance_from_file(filename: str) -> Optional[PerformanceData]:
    """Load performance data from a JSON file"""
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    performance_dir = os.path.join(script_dir, "performance_data")
    filepath = os.path.join(performance_dir, filename)
    
    if not os.path.exists(filepath):
        print(f"Performance file not found: {filename}")
        return None
    
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        # Extract previous config
        config_path = data.get('config_file_path', 'tower_defense_game.json')
        previous_config = load_previous_config(config_path)
        
        # Extract config details if available (new format)
        previous_config_details = data.get('previous_config_details', None)
        
        return PerformanceData(
            win_flag=data.get('win_flag', False),
            lives_remaining=data.get('lives_remaining', 0),
            starting_lives=data.get('starting_lives', 20),
            towers_built=data.get('towers_built', {}),
            tower_diversity=data.get('tower_diversity', 0),
            wave_reached=data.get('final_wave', 1),
            final_wave=data.get('final_wave', 1),
            previous_config=previous_config,
            previous_config_details=previous_config_details,
            # Load buff system metrics
            buff_encounters=data.get('buff_encounters'),
            buff_combinations_seen=data.get('buff_combinations_seen'),
            most_challenging_buffs=data.get('most_challenging_buffs'),
            tower_counter_effectiveness=data.get('tower_counter_effectiveness'),
            buff_adaptation_score=data.get('buff_adaptation_score', 0.0),
            enemy_buff_intensity=data.get('enemy_buff_intensity', 'none'),
            buff_related_deaths=data.get('buff_related_deaths', 0),
            effective_counter_strategies=data.get('effective_counter_strategies')
        )
    except Exception as e:
        print(f"Error loading performance data from {filename}: {e}")
        return None


def load_previous_config(config_path: str) -> Dict[str, Any]:
    """Load the configuration file that was used in the previous game"""
    # Try to find the config file
    possible_paths = [
        config_path,  # Full path as stored
        os.path.join('config', os.path.basename(config_path)),  # Just filename in config dir (for numbered configs)
        os.path.join('config', 'base', os.path.basename(config_path)),  # Base config dir
        os.path.join('config', 'variants', os.path.basename(config_path)),  # Variants dir
        os.path.join('config', 'base', 'tower_defense_game.json'),  # Default fallback
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            try:
                with open(path, 'r') as f:
                    config = json.load(f)
                print(f"   Using config from: {os.path.basename(path)}")
                return config
            except Exception as e:
                print(f"   Error loading config from {path}: {e}")
                continue
    
    # Final fallback to default config
    print("   Using fallback config: tower_defense_game.json")
    fallback_path = os.path.join('config', 'base', 'tower_defense_game.json')
    try:
        with open(fallback_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"   Error loading fallback config: {e}")
        return {}


def analyze_buff_performance_trends(performances: List[PerformanceData]) -> Dict[str, Any]:
    """Analyze buff system performance trends across multiple games"""
    if not performances:
        return {}
    
    # Filter performances that have buff data
    buff_performances = [p for p in performances if p.buff_encounters is not None]
    
    if not buff_performances:
        return {"message": "No buff data available in recent performances"}
    
    analysis = {
        "games_with_buffs": len(buff_performances),
        "total_games": len(performances),
        "buff_adoption_rate": len(buff_performances) / len(performances) * 100
    }
    
    # Analyze buff encounter frequency
    all_buff_encounters = {}
    all_combinations = []
    challenging_buffs = {}
    total_buffed_enemies = 0
    
    for perf in buff_performances:
        if perf.buff_encounters:
            total_buffed_enemies += sum(perf.buff_encounters.values())
            for buff, count in perf.buff_encounters.items():
                all_buff_encounters[buff] = all_buff_encounters.get(buff, 0) + count
        
        if perf.buff_combinations_seen:
            all_combinations.extend(perf.buff_combinations_seen)
        
        if perf.most_challenging_buffs:
            for buff in perf.most_challenging_buffs:
                challenging_buffs[buff] = challenging_buffs.get(buff, 0) + 1
    
    analysis.update({
        "total_buffed_enemies_encountered": total_buffed_enemies,
        "most_common_buffs": sorted(all_buff_encounters.items(), key=lambda x: x[1], reverse=True)[:5],
        "most_common_combinations": list(set(all_combinations)),
        "most_challenging_buffs": sorted(challenging_buffs.items(), key=lambda x: x[1], reverse=True)[:3]
    })

    # Analyze player adaptation
    adaptation_scores = [p.buff_adaptation_score for p in buff_performances if p.buff_adaptation_score > 0]
    if adaptation_scores and len(adaptation_scores) > 0:
        analysis["average_adaptation_score"] = sum(adaptation_scores) / len(adaptation_scores)
        analysis["adaptation_trend"] = "improving" if len(adaptation_scores) > 1 and adaptation_scores[-1] > adaptation_scores[0] else "stable"
    
    # Analyze buff intensity impact on performance
    intensity_performance = {}
    for perf in buff_performances:
        intensity = perf.enemy_buff_intensity
        if intensity not in intensity_performance:
            intensity_performance[intensity] = {"scores": [], "wins": 0, "total": 0}
        intensity_performance[intensity]["scores"].append(perf.score)
        intensity_performance[intensity]["total"] += 1
        if perf.win_flag:
            intensity_performance[intensity]["wins"] += 1
    
    for intensity, data in intensity_performance.items():
        if len(data["scores"]) > 0:
            data["average_score"] = sum(data["scores"]) / len(data["scores"])
        else:
            data["average_score"] = 0
        if data["total"] > 0:
            data["win_rate"] = data["wins"] / data["total"] * 100
        else:
            data["win_rate"] = 0
    
    analysis["intensity_performance"] = intensity_performance
    
    return analysis


def get_buff_difficulty_recommendations(performances: List[PerformanceData]) -> Dict[str, Any]:
    """Get recommendations for buff difficulty based on performance history"""
    analysis = analyze_buff_performance_trends(performances)
    
    if not analysis or "intensity_performance" not in analysis:
        return {
            "recommended_intensity": "medium",
            "reasoning": "No buff performance data available, using default"
        }
    
    intensity_data = analysis["intensity_performance"]
    recent_performances = performances[-3:] if len(performances) >= 3 else performances
    if len(recent_performances) > 0:
        recent_win_rate = sum(1 for p in recent_performances if p.win_flag) / len(recent_performances) * 100
    else:
        recent_win_rate = 0
    
    recommendations: Dict[str, Any] = {
        "current_performance": {
            "recent_win_rate": recent_win_rate,
            "total_games_analyzed": len(performances)
        }
    }
    
    # Determine recommended intensity
    if recent_win_rate >= 80:
        recommended_intensity = "high"
        reasoning = "High win rate suggests player is adapting well, can handle more challenge"
    elif recent_win_rate >= 60:
        recommended_intensity = "medium" 
        reasoning = "Good performance, maintaining current challenge level"
    elif recent_win_rate >= 40:
        recommended_intensity = "low"
        reasoning = "Moderate performance, reducing buff intensity for better balance"
    else:
        recommended_intensity = "very_low"
        reasoning = "Low win rate, significantly reducing buff challenge"
    
    recommendations["recommended_intensity"] = recommended_intensity
    recommendations["reasoning"] = reasoning
    
    # Specific buff recommendations
    if "most_challenging_buffs" in analysis and analysis["most_challenging_buffs"]:
        problematic_buffs = [buff for buff, count in analysis["most_challenging_buffs"]]
        if recent_win_rate < 50:
            recommendations["reduce_buffs"] = problematic_buffs[:2]
            recommendations["buff_adjustment"] = "Reduce frequency of most challenging buffs"
    
    if "most_common_buffs" in analysis:
        common_buffs = [buff for buff, count in analysis["most_common_buffs"]]
        recommendations["effective_buffs"] = common_buffs[:3]
    
    return recommendations


def generate_buff_config_from_performance(performances: List[PerformanceData]) -> Dict[str, Any]:
    """Generate buff configuration based on performance analysis"""
    recommendations = get_buff_difficulty_recommendations(performances)
    analysis = analyze_buff_performance_trends(performances)
    
    # Base configuration
    base_config = {
        "description": "AI-generated buff configuration based on performance analysis",
        "enabled": True,
        "scenario_type": "adaptive",
        "generation_metadata": {
            "generated_from_games": len(performances),
            "analysis_date": datetime.now().isoformat(),
            "performance_based": True
        }
    }
    
    # Determine intensity and adjust spawn rates
    intensity = recommendations.get("recommended_intensity", "medium")
    base_config["buff_intensity"] = intensity
    
    # Intensity multipliers
    intensity_multipliers = {
        "very_low": 0.3,
        "low": 0.6,
        "medium": 1.0,
        "high": 1.4,
        "extreme": 2.0
    }
    
    multiplier = intensity_multipliers.get(intensity, 1.0)
    
    # Generate spawn rates based on intensity
    spawn_rates = {
        "wave_ranges": {
            "1-10": {
                "base_chance": min(0.15, 0.05 * multiplier),
                "max_buffs": 1,
                "allowed_buffs": ["speed_boost", "armor"]
            },
            "11-20": {
                "base_chance": min(0.3, 0.15 * multiplier),
                "max_buffs": 2,
                "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration"]
            },
            "21-30": {
                "base_chance": min(0.45, 0.25 * multiplier),
                "max_buffs": 3,
                "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive"]
            },
            "31-40": {
                "base_chance": min(0.6, 0.35 * multiplier),
                "max_buffs": 4,
                "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance"]
            },
            "41-50": {
                "base_chance": min(0.75, 0.45 * multiplier),
                "max_buffs": 5,
                "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]
            },
            "51+": {
                "base_chance": min(0.9, 0.6 * multiplier),
                "max_buffs": min(7, int(5 * multiplier)),
                "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "fire_immunity", "berserker", "phase_shift"]
            }
        },
        "boss_multipliers": {
            "mini_boss": 2.0 * multiplier,
            "boss": 3.0 * multiplier,
            "super_boss": 4.0 * multiplier
        }
    }
    
    base_config["custom_spawn_rates"] = spawn_rates
    
    # Add featured combinations based on performance
    featured_combinations = ["stealth_assassin", "flying_fortress"]
    if intensity in ["high", "extreme"]:
        featured_combinations.extend(["regenerating_tank", "berserker_speedster"])
    if intensity == "extreme":
        featured_combinations.extend(["phase_wraith", "ultimate_defender"])
    
    base_config["featured_combinations"] = featured_combinations
    
    # Enable tracking
    base_config["buff_metrics_tracking"] = {
        "track_buff_effectiveness": True,
        "track_tower_counters": True,
        "track_player_adaptation": True
    }
    
    # Add performance context
    if recommendations:
        base_config["generation_context"] = {
            "recent_win_rate": recommendations.get("current_performance", {}).get("recent_win_rate", 0),
            "intensity_reasoning": recommendations.get("reasoning", ""),
            "challenging_buffs": recommendations.get("reduce_buffs", []),
            "effective_buffs": recommendations.get("effective_buffs", [])
        }
    
    return base_config


def get_manual_performance_input() -> PerformanceData:
    """Get performance data via manual input"""
    print("\n📝 Manual Performance Input")
    print("Enter your performance data for the level you just played:")
    
    # Win/Loss
    while True:
        win_input = input("Did you win? (y/n): ").strip().lower()
        if win_input in ['y', 'yes', '1', 'true']:
            win_flag = True
            break
        elif win_input in ['n', 'no', '0', 'false']:
            win_flag = False
            break
        else:
            print("Please enter 'y' for yes or 'n' for no")
    
    # Lives
    while True:
        try:
            starting_lives = int(input("Starting lives (default 20): ") or "20")
            lives_remaining = int(input(f"Lives remaining (0-{starting_lives}): "))
            if 0 <= lives_remaining <= starting_lives:
                break
            else:
                print(f"Lives remaining must be between 0 and {starting_lives}")
        except ValueError:
            print("Please enter valid numbers")
    
    # Tower diversity (simplified)
    while True:
        try:
            tower_types_used = int(input("How many different tower types did you build? (0-14): "))
            if 0 <= tower_types_used <= 14:
                break
            else:
                print("Tower types must be between 0 and 14")
        except ValueError:
            print("Please enter a valid number")
    
    # Create simplified towers_built dict
    towers_built = {}
    for i in range(tower_types_used):
        towers_built[f"tower_type_{i}"] = 1
    
    # Load default config as previous config
    previous_config = load_previous_config("config/base/tower_defense_game.json")
    
    return PerformanceData(
        win_flag=win_flag,
        lives_remaining=lives_remaining,
        starting_lives=starting_lives,
        towers_built=towers_built,
        tower_diversity=tower_types_used,
        previous_config=previous_config
    ) 