import pygame
import sys
import os
from typing import List, <PERSON>ple
import random
import time
import json
from datetime import datetime

from enemies import Enemy
from towers import Tower
from projectiles import Projectile
from game_systems import Map, WaveManager, UIManager, TowerManager
from game_systems.tower_upgrade_system import TowerUpgradeSystem
from game_systems.upgrade_ui import UpgradeUI
from game_systems.rock_removal_rewards import RockRemovalRewards
from game_systems.global_upgrade_system import GlobalUpgradeSystem
from game_systems.global_upgrade_ui import GlobalUpgradeUI
from ai.performance_analysis import PerformanceData

class Game:
    """Main game controller - coordinates between all game systems"""
    
    def __init__(self, skip_config_selection=False, launched_from_menu=False):
        pygame.init()
        
        # Track if game was launched from the main menu launcher
        self.launched_from_menu = launched_from_menu
        
        # Let player choose config before starting (unless called from launcher)
        if not skip_config_selection:
            self.choose_game_config()
        
        # Load game configuration
        from config.game_config import _load_config, get_current_config_path
        self.full_config = _load_config()  # Load the entire config
        self.game_config = self.full_config.get('game_config', {})  # Extract game_config section
        self.config_file_path = get_current_config_path()  # Track which config we're using

        # Screen setup with fullscreen support
        self.fullscreen = False
        self.SCREEN_WIDTH = 1200
        self.SCREEN_HEIGHT = 800
        
        # Get display info for fullscreen
        info = pygame.display.Info()
        self.fullscreen_width = info.current_w
        self.fullscreen_height = info.current_h
        
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("Tower Defense Game")
        self.clock = pygame.time.Clock()
        self.FPS = 60
        
        # Game state using config values
        self.running = True
        self.paused = False
        self.game_over = False
        self.victory = False
        self.money = self.game_config.get('starting_money', 20)
        self.lives = self.game_config.get('starting_lives', 20)
        
        # Performance tracking for adaptive AI
        self.starting_money = self.money
        self.starting_lives = self.lives
        self.total_money_spent = 0
        self.total_money_earned = self.starting_money  # Include starting money as earned

        # Game timing for accurate duration tracking
        import time
        self.game_start_time = time.time()
        self.tower_build_order = []  # Track order of tower construction
        
        # Speed control
        self.game_speed = 1  # 1 = normal speed, 2 = double speed
        self.speed_options = [1, 2]  # Available speed options
        self.current_speed_index = 0  # Index in speed_options
        
        # Victory/Game Over state
        self.show_victory_screen = False
        self.show_game_over_screen = False
        self.restart_requested = False
        self.exit_to_menu_requested = False
        
        # Wave completion tracking
        self.completed_wave_number = 0  # Track which wave was just completed
        
        # Performance monitoring
        self.fps_counter = 0
        self.fps_timer = 0
        self.current_fps = 60
        self.frame_time_samples = []
        self.max_frame_time_samples = 60  # Track last 60 frames
        
        # Game objects
        self.enemies: List[Enemy] = []
        self.towers: List[Tower] = []
        self.projectiles: List[Projectile] = []
        
        # Initialize game systems
        self.map = Map(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.wave_manager = WaveManager(self.map.get_path())
        self.tower_manager = TowerManager()
        # Initialize tower costs for wave 1
        self.tower_manager.set_current_wave(1)
        self.ui_manager = UIManager(self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.tower_manager)
        self.upgrade_system = TowerUpgradeSystem()
        self.upgrade_ui = UpgradeUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        
        # Initialize performance optimizer
        from game_systems.performance_optimizer import PerformanceOptimizer
        self.performance_optimizer = PerformanceOptimizer(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.performance_optimizer.set_optimization_level('auto')  # Start with auto optimization
        
        # Initialize sprite manager for enhanced graphics
        from game_systems.sprite_manager import SpriteManager
        self.sprite_manager = SpriteManager()
        
        # Load sprites immediately at game startup
        self.sprite_manager.load_all_sprites()
        
        # Set sprite manager for wave manager (for enemy sprites)
        self.wave_manager.set_sprite_manager(self.sprite_manager)
        
        # Ensure any existing towers get the sprite manager
        for tower in self.towers:
            if hasattr(tower, 'set_sprite_manager'):
                tower.set_sprite_manager(self.sprite_manager)
        
        # UI state
        self.show_wave_complete = False
        self.wave_complete_timer = 0
        self.wave_bonus = 0
        
        # Rock removal system
        self.rock_removal_cost = self.calculate_rock_removal_cost()
        
        # Rock removal rewards system
        self.rock_removal_rewards = RockRemovalRewards()
        
        # Health immunity system (from rock removal rewards)
        self.health_immunity_active = False
        self.health_immunity_timer = 0
        
        # Global upgrade system
        # Use existing system if passed from launcher, otherwise create new one
        if not hasattr(self, 'global_upgrade_system'):
            self.global_upgrade_system = GlobalUpgradeSystem()
        self.global_upgrade_ui = GlobalUpgradeUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.global_upgrade_system)
        
        # Victory screen state
        self.show_victory_points = False
        self.victory_points_earned = 0
        self.victory_difficulty = 0
        
        # Tower ID counter for global upgrades
        self.tower_id_counter = 0
        
        self.screenshake_intensity = 0
        self.screenshake_timer = 0
        self.screenshake_offset = (0, 0)
    
    def choose_game_config(self):
        """Let player choose which config file to use"""
        from config.game_config import list_available_configs, set_config_file, get_current_config_file
        
        configs = list_available_configs()
        if len(configs) <= 1:
            # Only one config available, use default
            return
        
        print("=== Tower Defense Game ===")
        print("Choose your game configuration:")
        print()
        
        for i, config in enumerate(configs, 1):
            # Try to get a readable name from the config
            config_name = config.replace('.json', '').replace('_', ' ').title()
            if 'demo' in config.lower():
                config_name += " (Demo)"
            elif 'adaptive' in config.lower():
                config_name += " (AI-Generated)"
            elif 'nightmare' in config.lower():
                config_name += " (Very Hard)"
            elif 'hard' in config.lower():
                config_name += " (Hard)"
            elif 'normal' in config.lower():
                config_name += " (Normal)"
            elif 'tutorial' in config.lower():
                config_name += " (Easy)"
            elif 'casual' in config.lower():
                config_name += " (Very Easy)"
            
            print(f"   {i}. {config_name}")
        
        print(f"   {len(configs) + 1}. Use default ({get_current_config_file()})")
        print()
        
        while True:
            try:
                choice = input(f"Select configuration (1-{len(configs) + 1}): ").strip()
                
                if choice == str(len(configs) + 1):
                    # Use default
                    break
                
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(configs):
                    selected_config = configs[choice_idx]
                    set_config_file(selected_config)
                    print(f"Selected: {selected_config}")
                    break
                else:
                    print("❌ Invalid choice. Please try again.")
                    
            except ValueError:
                print("❌ Please enter a valid number.")
            except KeyboardInterrupt:
                print("\nUsing default configuration.")
                break
        
        print("Starting game...")
        print()
    
    def handle_events(self):
        """Handle all game events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            # Handle enemy lookup events first (they can consume events)
            elif self.ui_manager.handle_enemy_lookup_event(event):
                continue  # Event was handled by enemy lookup system
            
            elif event.type == pygame.KEYDOWN:
                self.handle_key_press(event.key)
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    self.handle_mouse_click(event.pos)
                elif event.button == 4:  # Mouse wheel up
                    self.ui_manager.handle_scroll(-1)
                elif event.button == 5:  # Mouse wheel down
                    self.ui_manager.handle_scroll(1)
            
            elif event.type == pygame.MOUSEWHEEL:
                # Handle newer pygame wheel events
                self.ui_manager.handle_scroll(event.y)
            
            elif event.type == pygame.MOUSEMOTION:
                # Update mouse position for UI hover effects
                self.ui_manager.update_mouse_pos(event.pos)
                self.upgrade_ui.update_mouse_pos(event.pos)
    
    def handle_key_press(self, key):
        """Handle keyboard input - only essential keys"""
        if key == pygame.K_SPACE:
            # Handle victory points screen
            if self.show_victory_points:
                self.show_victory_points = False
                self.global_upgrade_ui.open_menu()
                return
            
            # Check if we can start the next wave first (manual wave start)
            if self.wave_manager.can_start_next_wave():
                wave_info = self.wave_manager.start_next_wave_manual()
                if wave_info:
                    # Update tower costs for the new wave
                    current_wave = wave_info.get('wave_number', 1)
                    self.tower_manager.set_current_wave(current_wave)
                return
            else:
                # Normal pause/unpause if not waiting for wave start
                self.paused = not self.paused
        
        elif key == pygame.K_ESCAPE:
            if self.game_over or self.victory:
                self.running = False
            elif self.global_upgrade_ui.is_open:
                self.global_upgrade_ui.close_menu()
            else:
                self.tower_manager.cancel_placement()
        
        elif key == pygame.K_r:
            if self.game_over or self.victory:
                self.restart_requested = True
        
        elif key == pygame.K_F1:
            self.toggle_fullscreen()
        
        elif key == pygame.K_TAB:
            self.toggle_game_speed()
        
        elif key == pygame.K_o:  # Toggle optimization level
            self.toggle_optimization_level()
        
        # Rock removal rewards debug controls
        elif key == pygame.K_F2:
            # Toggle debug mode
            if self.rock_removal_rewards.debug_mode:
                self.rock_removal_rewards.disable_debug_mode()
            else:
                self.rock_removal_rewards.enable_debug_mode()
        
        elif key == pygame.K_r and self.rock_removal_rewards.debug_mode:
            # Force rock removal reward (random)
            import random
            reward_types = ['money_bonus', 'tower_boost', 'enemy_boost', 'shield_boost', 'freeze_enemies', 'super_speed']
            reward_type = random.choice(reward_types)
            self.rock_removal_rewards.force_reward(reward_type)
        
        elif key == pygame.K_1 and self.rock_removal_rewards.debug_mode:
            # Force money bonus
            self.rock_removal_rewards.force_reward('money_bonus')
        
        elif key == pygame.K_2 and self.rock_removal_rewards.debug_mode:
            # Force tower boost
            self.rock_removal_rewards.force_reward('tower_boost')
        
        elif key == pygame.K_3 and self.rock_removal_rewards.debug_mode:
            # Force enemy boost
            self.rock_removal_rewards.force_reward('enemy_boost')
        
        elif key == pygame.K_4 and self.rock_removal_rewards.debug_mode:
            # Force shield boost
            self.rock_removal_rewards.force_reward('shield_boost')
        
        elif key == pygame.K_5 and self.rock_removal_rewards.debug_mode:
            # Force freeze enemies
            self.rock_removal_rewards.force_reward('freeze_enemies')
        
        elif key == pygame.K_6 and self.rock_removal_rewards.debug_mode:
            # Force super speed
            self.rock_removal_rewards.force_reward('super_speed')
        
        elif key == pygame.K_0 and self.rock_removal_rewards.debug_mode:
            # Disable test mode
            self.rock_removal_rewards.disable_test_mode()
        
        elif key == pygame.K_t and self.rock_removal_rewards.debug_mode:
            # Test rock removal rewards without removing rocks
            self.test_rock_removal_rewards()
    
    def toggle_fullscreen(self):
        """Toggle between fullscreen and windowed mode"""
        self.fullscreen = not self.fullscreen
        
        if self.fullscreen:
            # Switch to fullscreen
            self.screen = pygame.display.set_mode((self.fullscreen_width, self.fullscreen_height), pygame.FULLSCREEN)
            # Update screen dimensions for game systems
            old_width, old_height = self.SCREEN_WIDTH, self.SCREEN_HEIGHT
            self.SCREEN_WIDTH = self.fullscreen_width
            self.SCREEN_HEIGHT = self.fullscreen_height
        else:
            # Switch to windowed
            self.screen = pygame.display.set_mode((1200, 800))
            # Restore original dimensions
            self.SCREEN_WIDTH = 1200
            self.SCREEN_HEIGHT = 800
        
        # Reinitialize systems with new screen dimensions
        self.map = Map(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.ui_manager = UIManager(self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.tower_manager)
        self.upgrade_ui = UpgradeUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        
        # Update wave manager with new path
        self.wave_manager = WaveManager(self.map.get_path())
        self.wave_manager.set_sprite_manager(self.sprite_manager)
    
    def toggle_game_speed(self):
        """Toggle between different game speeds"""
        self.current_speed_index = (self.current_speed_index + 1) % len(self.speed_options)
        self.game_speed = self.speed_options[self.current_speed_index]
    
    def toggle_optimization_level(self):
        """Toggle between optimization levels"""
        levels = ['auto', 'low', 'medium', 'high']
        current_index = levels.index(self.performance_optimizer.optimization_level)
        next_index = (current_index + 1) % len(levels)
        new_level = levels[next_index]
        
        self.performance_optimizer.set_optimization_level(new_level)
        print(f"Optimization level: {new_level}")
    
    def handle_mouse_click(self, pos):
        """Handle mouse clicks"""
        # Handle global upgrade UI clicks first
        if self.global_upgrade_ui.is_open:
            self.global_upgrade_ui.handle_click(pos)
            return
        
        # Handle restart and exit to menu button clicks for victory/game over screens
        if self.game_over or self.victory:
            restart_clicked = self.handle_restart_button_click(pos)
            exit_clicked = self.handle_exit_to_menu_button_click(pos)
            
            if restart_clicked:
                self.restart_requested = True
            elif exit_clicked:
                self.exit_to_menu_requested = True
            return
        
        if self.paused:
            return
        
        # Check for upgrade UI clicks first (highest priority)
        click_result = self.upgrade_ui.handle_click(pos, self.upgrade_system, self.money)
        if click_result['action'] == 'remove_tower':
            self.remove_tower(click_result['tower'])
            return
        elif click_result['action'] == 'upgrade':
            if click_result['success']:
                # Deduct money cost from player
                money_cost = click_result.get('money_cost', 0)
                self.money -= money_cost
                self.total_money_spent += money_cost  # Track for performance analysis
            return
        
        # Check for speed button click
        if self.ui_manager.handle_speed_button_click(pos):
            self.toggle_game_speed()
            return
        
        # Check for rock removal button click
        if self.ui_manager.handle_rock_removal_button_click(pos):
            return
        
        # Check for enemy info button click
        if self.ui_manager.handle_enemy_info_button_click(pos):
            return
        
        # Check for upgrade all button click
        if self.ui_manager.handle_upgrade_all_button_click(pos):
            self.handle_upgrade_all_towers()
            return
        
        # Check for tower bar clicks
        clicked_tower_index = self.ui_manager.handle_tower_bar_click(pos)
        if clicked_tower_index is not None:
            tower_type = self.ui_manager.get_selected_tower_type()
            if tower_type:
                self.tower_manager.select_tower_type(tower_type)
            return
        
        # Check for tower clicks (for upgrade panel)
        if self.ui_manager.handle_tower_click(pos, self.towers):
            # Set the selected tower for upgrades
            selected_tower = self.ui_manager.selected_placed_tower
            self.upgrade_ui.set_selected_tower(selected_tower)
            return
        else:
            # Clear upgrade selection if clicking elsewhere
            self.upgrade_ui.clear_selection()
        
        # Handle rock removal or tower placement
        if self.ui_manager.is_rock_removal_mode():
            # Try to remove a rock
            if self.attempt_rock_removal(pos):
                pass  # Rock was successfully removed
        elif self.tower_manager.placing_tower:
            self.attempt_tower_placement(pos)
    
    def handle_restart_button_click(self, mouse_pos) -> bool:
        """Check if restart button was clicked"""
        # Victory/Game Over screen restart button (left side)
        if self.show_victory_screen or self.show_game_over_screen or self.game_over or self.victory:
            # These coordinates MUST match the UI renderer exactly
            button_width = 180
            button_height = 50
            button_spacing = 20
            
            if self.victory or self.show_victory_screen:
                # Victory screen button position
                panel_height = 300
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 200
            else:
                # Game over screen button position
                panel_height = 250
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 170
            
            # Position restart button on the left
            button_x = (self.SCREEN_WIDTH - (2 * button_width + button_spacing)) // 2
            
            if (button_x <= mouse_pos[0] <= button_x + button_width and
                button_y <= mouse_pos[1] <= button_y + button_height):
                return True
        
        return False
    
    def handle_exit_to_menu_button_click(self, mouse_pos) -> bool:
        """Check if exit to menu button was clicked"""
        # Victory/Game Over screen exit to menu button (right side)
        if self.show_victory_screen or self.show_game_over_screen or self.game_over or self.victory:
            # These coordinates MUST match the UI renderer exactly
            button_width = 180
            button_height = 50
            button_spacing = 20
            
            if self.victory or self.show_victory_screen:
                # Victory screen button position
                panel_height = 300
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 200
            else:
                # Game over screen button position
                panel_height = 250
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 170
            
            # Position exit to menu button on the right
            button_x = (self.SCREEN_WIDTH - (2 * button_width + button_spacing)) // 2 + button_width + button_spacing
            
            if (button_x <= mouse_pos[0] <= button_x + button_width and
                button_y <= mouse_pos[1] <= button_y + button_height):
                return True
        
        return False
    
    def attempt_tower_placement(self, pos):
        """Try to place a tower at the given position"""
        success, tower, cost = self.tower_manager.attempt_tower_placement(
            pos, self.money, self.towers, self.map
        )
        
        if success and tower:
            # Assign unique tower ID for global upgrades
            self.tower_id_counter += 1
            if hasattr(tower, 'tower_type'):
                tower.tower_id = f"{tower.tower_type}_{self.tower_id_counter}"  # type: ignore
                # Track tower build order for performance analysis
                self.tower_build_order.append(tower.tower_type)
            # Apply global upgrades to the tower
            if hasattr(tower, 'apply_upgrades_to_tower'):
                self.global_upgrade_system.apply_upgrades_to_tower(tower)
            # Set upgrade system reference for currency generation
            if hasattr(tower, 'set_upgrade_system_reference'):
                tower.set_upgrade_system_reference(self.upgrade_system)  # type: ignore
            # Set sprite manager for enhanced graphics
            if hasattr(tower, 'set_sprite_manager'):
                tower.set_sprite_manager(self.sprite_manager)
            self.towers.append(tower)
            self.money -= cost
            self.total_money_spent += cost  # Track money spent for performance analysis
            
            # Apply current tower boost if active
            self.apply_boost_to_new_tower(tower)
    
    def remove_tower(self, tower):
        """Remove a tower and refund 50% of its cost"""
        if tower not in self.towers:
            return
        
        # Calculate refund (50% of current tower cost)
        tower_type = tower.tower_type
        current_cost = self.tower_manager.get_tower_cost(tower_type)
        refund = int(current_cost * 0.5)
        
        # Remove tower from list
        self.towers.remove(tower)
        
        # Give refund to player
        self.money += refund
        
        # Decrease tower count for dynamic pricing
        if tower_type in self.tower_manager.towers_built_count:
            self.tower_manager.towers_built_count[tower_type] = max(0, 
                self.tower_manager.towers_built_count[tower_type] - 1)
        
        # Clear upgrade UI selection
        self.upgrade_ui.clear_selection()
    
    def handle_upgrade_all_towers(self):
        """Handle upgrade all towers button click"""
        if not self.towers:
            return  # No towers to upgrade
        
        # Use the upgrade UI's upgrade all functionality
        upgrade_result = self.upgrade_ui.upgrade_all_towers(self.towers, self.upgrade_system, self.money)
        
        if upgrade_result['success']:
            # Deduct money spent
            money_spent = upgrade_result['total_money_spent']
            self.money -= money_spent
            self.total_money_spent += money_spent  # Track for performance analysis
            
            # Show feedback to player
            towers_upgraded = upgrade_result['towers_upgraded']
            print(f"Upgraded {towers_upgraded} towers for ${money_spent}")
            
            # If we have any specific UI feedback system, we could add it here
            # For example, showing a brief notification or updating visual indicators
        else:
            print("No towers could be upgraded (insufficient resources or all towers are max level)")
    
    def update_enemies(self):
        """Update all enemies"""
        enemies_to_add = []
        
        for enemy in self.enemies[:]:
            # Pass game speed to enemy update for faster movement
            if hasattr(enemy, 'update_with_speed'):
                enemy.update_with_speed(self.game_speed)
            else:
                enemy.update()
            
            # Handle burn damage effects (moved from flame tower to prevent multiple processing)
            if hasattr(enemy, 'burn_timer') and enemy.burn_timer > 0:
                enemy.burn_timer -= self.game_speed
                # Apply burn damage every 1/3 second (20 frames at normal speed)
                if enemy.burn_timer % 20 <= self.game_speed:
                    if hasattr(enemy, 'burn_damage') and enemy.burn_damage > 0:
                        enemy.take_damage(enemy.burn_damage, 'flame')
            
            # Handle poison effects (adjust timer based on speed)
            if hasattr(enemy, 'poison_timer') and enemy.poison_timer > 0:
                enemy.poison_timer -= self.game_speed
                if hasattr(enemy, 'poison_damage_timer'):
                    enemy.poison_damage_timer += self.game_speed
                    if enemy.poison_damage_timer >= 60:  # Every second
                        enemy.take_damage(enemy.poison_damage)
                        enemy.poison_damage_timer = 0
            
            # Handle boss minion spawning (while boss is alive)
            if hasattr(enemy, 'should_spawn_minions') and enemy.should_spawn_minions():
                minion_count = enemy.get_minion_count() if hasattr(enemy, 'get_minion_count') else 0  # type: ignore
                for i in range(minion_count):
                    from enemies import BasicEnemy
                    minion = BasicEnemy(self.map.get_path())
                    minion.x = enemy.x + (i - minion_count/2) * 30
                    minion.y = enemy.y
                    if hasattr(minion, 'set_map_reference'):
                        minion.set_map_reference(self.map)  # type: ignore
                    enemies_to_add.append(minion)
            
            # Handle TimeLord Boss echo spawning
            if hasattr(enemy, 'should_spawn_echoes') and callable(getattr(enemy, 'should_spawn_echoes', None)):
                spawnable_rifts = enemy.should_spawn_echoes()  # type: ignore
                for rift in spawnable_rifts:
                    from enemies import BasicEnemy
                    echo = BasicEnemy(self.map.get_path())
                    echo.x = rift['x']
                    echo.y = rift['y']
                    if hasattr(echo, 'health'):
                        echo.health = echo.health * 0.5  # type: ignore
                    if hasattr(echo, 'color'):
                        echo.color = (150, 0, 255)  # type: ignore
                    if hasattr(echo, 'set_map_reference'):
                        echo.set_map_reference(self.map)  # type: ignore
                    enemies_to_add.append(echo)
            
            # Handle Necromancer Boss undead summoning
            if hasattr(enemy, 'should_summon_undead') and enemy.should_summon_undead():
                from enemies import BasicEnemy
                undead = BasicEnemy(self.map.get_path())
                undead.x = enemy.x + random.uniform(-60, 60)
                undead.y = enemy.y + random.uniform(-60, 60)
                if hasattr(undead, 'color'):
                    undead.color = (100, 100, 50)  # type: ignore
                if hasattr(undead, 'health'):
                    undead.health = undead.health * 0.7  # type: ignore
                if hasattr(undead, 'set_map_reference'):
                    undead.set_map_reference(self.map)  # type: ignore
                enemies_to_add.append(undead)
            
            # Handle enemy death and removal
            if enemy.health <= 0:
                # Register dead enemy with Necromancer bosses for resurrection
                for boss in self.enemies:
                    if hasattr(boss, 'register_dead_enemy'):
                        boss.register_dead_enemy(type(enemy).__name__, enemy.x, enemy.y)
                
                # Check if enemy reached end or was killed
                if not enemy.reached_end:
                    # Enemy was killed - award money
                    self.money += enemy.reward
                    self.total_money_earned += enemy.reward  # Track money earned for performance analysis
                    
                    # Handle splitting enemies or other on_death mechanics
                    if hasattr(enemy, 'on_death'):
                        spawned_enemies = enemy.on_death()
                        if spawned_enemies:
                            for spawned_enemy in spawned_enemies:
                                if hasattr(spawned_enemy, 'set_map_reference'):
                                    spawned_enemy.set_map_reference(self.map)  # type: ignore
                                    enemies_to_add.append(spawned_enemy)
                else:
                    # Enemy reached end - lose lives unless health immunity is active
                    if not self.health_immunity_active:
                        self.lives -= 1
                        self.trigger_screenshake(intensity=16, duration=22)
                    if self.lives <= 0:
                        self.game_over = True
                        self.show_game_over_screen = True
                        # Save performance data for adaptive AI
                        self.save_performance_data()
                
                self.enemies.remove(enemy)
            elif enemy.reached_end:
                # Enemy reached the end - lose lives unless health immunity is active
                if not self.health_immunity_active:
                    self.lives -= 1
                    self.trigger_screenshake(intensity=16, duration=22)
                if self.lives <= 0:
                    self.game_over = True
                    self.show_game_over_screen = True
                    # Save performance data for adaptive AI
                    self.save_performance_data()
                self.enemies.remove(enemy)
        
        # Add any spawned enemies
        self.enemies.extend(enemies_to_add)
    
    def update_towers(self):
        """Update all towers"""
        # First, clear all detection flags before detector towers update them
        for enemy in self.enemies:
            if hasattr(enemy, 'detected_by_detector'):
                enemy.detected_by_detector = False
        
        # Separate towers by type to ensure detector towers update first
        detector_towers = [tower for tower in self.towers if tower.tower_type == 'detector']
        other_towers = [tower for tower in self.towers if tower.tower_type != 'detector']
        
        # Update detector towers first
        for tower in detector_towers:
            # Pass game speed to tower update for faster firing with optimizations
            if hasattr(tower, 'update_with_speed_optimized'):
                tower.update_with_speed_optimized(self.enemies, self.projectiles, self.game_speed)
            elif hasattr(tower, 'update_with_speed'):
                tower.update_with_speed(self.enemies, self.projectiles, self.game_speed)
            else:
                tower.update(self.enemies, self.projectiles)
            
            # Detector towers handle their own currency generation through detection
        
        # Then update other towers (they can now see detected enemies)
        for tower in other_towers:
            # Track damage before update (skip towers that handle their own currency)
            if tower.tower_type not in ['laser']:
                previous_damage = tower.total_damage_dealt
            
            # Pass game speed to tower update for faster firing with optimizations
            if hasattr(tower, 'update_with_speed_optimized'):
                tower.update_with_speed_optimized(self.enemies, self.projectiles, self.game_speed)
            elif hasattr(tower, 'update_with_speed'):
                tower.update_with_speed(self.enemies, self.projectiles, self.game_speed)
            else:
                tower.update(self.enemies, self.projectiles)
            
            # Check if tower dealt damage directly (not through projectiles)
            # Skip towers that handle their own damage tracking (laser, etc.)
            if tower.tower_type not in ['laser']:
                damage_this_frame = tower.total_damage_dealt - previous_damage
                if damage_this_frame > 0:
                    # Use centralized currency generation (already includes 5/100 nerf)
                    tower.track_damage_and_generate_currency(damage_this_frame)
    
    def update_projectiles(self):
        """Update all projectiles"""
        for projectile in self.projectiles[:]:
            # Handle different projectile update methods with speed
            if hasattr(projectile, 'update') and callable(getattr(projectile, 'update')):
                if hasattr(projectile, 'update_homing'):
                    if hasattr(projectile, 'update_homing_with_speed'):
                        projectile.update_homing_with_speed(self.enemies, self.game_speed)  # type: ignore
                    else:
                        projectile.update_homing(self.enemies)  # type: ignore
                elif hasattr(projectile.__class__, 'update') and len(projectile.update.__code__.co_varnames) > 1:
                    # Missile projectiles need enemies parameter
                    if hasattr(projectile, 'update_with_speed'):
                        projectile.update_with_speed(self.enemies, self.game_speed)  # type: ignore
                    else:
                        projectile.update(self.enemies)  # type: ignore
                else:
                    if hasattr(projectile, 'update_with_speed'):
                        projectile.update_with_speed(self.game_speed)  # type: ignore
                    else:
                        projectile.update()  # type: ignore
            
            # Check projectile collisions with enemies
            if hasattr(projectile, 'check_collision'):
                collision_result = projectile.check_collision(self.enemies)
                
                # Handle damage tracking for currency generation
                if isinstance(collision_result, dict) and collision_result.get('hit'):
                    damage_dealt = collision_result.get('damage', 0)
                    tower_id = collision_result.get('tower_id')
                    
                    # Trigger screenshake for explosive tower only
                    if hasattr(projectile, 'tower_type'):
                        if projectile.tower_type in ['explosive']:
                            # Large explosion screenshake
                            self.trigger_screenshake(intensity=14, duration=20)
                    
                    # Ensure damage_dealt is not None and is a number
                    if damage_dealt is None:
                        damage_dealt = 0
                    
                    if tower_id:
                        # Find the tower and use centralized currency generation
                        tower = self._find_tower_by_id(tower_id)
                        if tower:
                            if damage_dealt > 0:
                                # Use centralized damage tracking and currency generation
                                tower.track_damage_and_generate_currency(damage_dealt)
                            else:
                                # Support towers get minimal currency for successful hits
                                tower.track_utility_hit()
            
            # Check for screenshake flags set by projectiles (for explosions that bypass collision system)
            if hasattr(projectile, 'should_trigger_screenshake') and projectile.should_trigger_screenshake:
                if hasattr(projectile, 'tower_type') and projectile.tower_type in ['explosive']:
                    # Large explosion screenshake
                    self.trigger_screenshake(intensity=14, duration=20)
                # Reset the flag
                projectile.should_trigger_screenshake = False
            
            if hasattr(projectile, 'should_remove') and projectile.should_remove:
                self.projectiles.remove(projectile)
    
    def _find_tower_by_id(self, tower_id: str):
        """Find a tower by its unique ID"""
        for tower in self.towers:
            if hasattr(tower, 'tower_id') and tower.tower_id == tower_id:
                return tower
        return None
    
    def update_waves(self):
        """Update wave management"""
        # Spawn new enemies (adjusted for speed)
        new_enemy = self.wave_manager.spawn_enemy(self.game_speed)
        if new_enemy:
            # Set map reference for terrain effects
            if hasattr(new_enemy, 'set_map_reference'):
                new_enemy.set_map_reference(self.map)  # type: ignore
            
            # Apply current enemy boost if active
            if hasattr(self, 'apply_boost_to_new_enemy'):
                self.apply_boost_to_new_enemy(new_enemy)  # type: ignore
            
            # Trigger screenshake for boss spawns
            if hasattr(new_enemy, 'damage_reduction') and new_enemy.damage_reduction > 0.2:
                self.trigger_screenshake(intensity=18, duration=25)
            
            self.enemies.append(new_enemy)
        
        # Update rock removal cost when wave number changes
        current_wave = self.wave_manager.get_wave_info().get('wave_number', 1)
        if not hasattr(self, '_last_wave_for_cost') or self._last_wave_for_cost != current_wave:
            self.rock_removal_cost = self.calculate_rock_removal_cost()
            self._last_wave_for_cost = current_wave
        
        # Check for wave completion
        wave_info = self.wave_manager.update(self.enemies)
        if wave_info:
            if wave_info.get('game_completed', False):
                # Player has beaten the final wave!
                self.victory = True
                self.show_victory_screen = True
                
                # Calculate and award victory points based on difficulty
                difficulty = self.full_config.get('game_config', {}).get('difficulty', 50)
                points_earned = self.global_upgrade_system.award_victory_points(difficulty)
                
                # Show victory points screen
                self.show_victory_points = True
                self.victory_points_earned = points_earned
                self.victory_difficulty = difficulty
                
                # Save performance data for adaptive AI
                self.save_performance_data()
                
                # Check for first-time win and generate new level if needed
                self.check_first_win_and_generate_level()
            elif wave_info.get('wave_completed', False):
                # Normal wave completion - store the completed wave number BEFORE incrementing
                self.completed_wave_number = wave_info['wave_number']
                self.money += wave_info['money_bonus']
                self.total_money_earned += wave_info['money_bonus']  # Track wave bonus for performance analysis
                self.wave_bonus = wave_info['money_bonus']
                self.show_wave_complete = True
                self.wave_complete_timer = 180  # Show for 3 seconds
                
                # Update rock removal cost for new wave (hefty late-game scaling)
                self.rock_removal_cost = self.calculate_rock_removal_cost()
                
                # Reset rock removal counter for new wave
                self.rock_removal_rewards.reset_wave_counter()
                
                # Manual wave start: Don't auto-start the next wave
                # Player must press SPACE to start the next wave
    
    def update_ui_state(self):
        """Update UI-related timers and state"""
        if self.show_wave_complete:
            self.wave_complete_timer -= self.game_speed
            if self.wave_complete_timer <= 0:
                self.show_wave_complete = False
    
    def update(self):
        """Update all game systems - FIXED: No longer runs multiple times per frame"""
        # Handle restart request
        if self.restart_requested:
            self.restart_game()
            return
            
        # Handle exit to menu request
        if self.exit_to_menu_requested:
            self.running = False
            return
        
        # Don't update if game is over or won
        if self.paused or self.game_over or self.victory:
            return
        
        # Update rock removal effects
        self.rock_removal_rewards.update_effects(self.game_speed, self.towers, self.enemies, self)
        
        # Update health immunity timer
        if self.health_immunity_active:
            self.health_immunity_timer -= self.game_speed
            if self.health_immunity_timer <= 0:
                self.remove_health_immunity()
        
        # Single update pass - entities handle speed internally
        self.update_enemies()
        self.update_towers()
        self.update_projectiles()
        self.update_waves()
        self.update_ui_state()
    
    def draw_game_objects(self, surface):
        """Draw all game objects"""
        # Draw enemies
        for enemy in self.enemies:
            enemy.draw(surface)
        
        # Draw towers with selection state
        selected_tower = self.ui_manager.selected_placed_tower
        for tower in self.towers:
            is_selected = (tower == selected_tower)
            tower.draw(surface, selected=is_selected)
        
        # Draw projectiles
        for projectile in self.projectiles:
            projectile.draw(surface)
    
    def get_game_state(self) -> dict:
        """Get current game state for UI rendering"""
        wave_info = self.wave_manager.get_wave_info()
        placement_state = self.tower_manager.get_placement_state()
        
        # Add total waves information to wave_info for UI display
        total_waves = self.full_config.get('wave_config', {}).get('total_waves', 80)  # Default to 80 if not found
        wave_info['total_waves'] = total_waves
        
        return {
            'money': self.money,
            'lives': self.lives,
            'wave_info': wave_info,
            'paused': self.paused,
            'game_over': self.game_over,
            'victory': self.victory,
            'show_victory_screen': self.show_victory_screen,
            'show_game_over_screen': self.show_game_over_screen,
            'selected_tower': placement_state['selected_tower_type'],
            'show_wave_complete': self.show_wave_complete,
            'completed_wave_number': self.completed_wave_number,
            'wave_bonus': self.wave_bonus,
            'towers': self.towers,
            'game_speed': self.game_speed,
            'performance': self.get_performance_info(),
            'adaptive_performance': self.get_adaptive_performance_data(),
            'waiting_for_next_wave': self.wave_manager.waiting_for_next_wave,
            'can_start_wave': self.wave_manager.can_start_next_wave(),
            'rock_removal_cost': self.rock_removal_cost
        }
    
    def update_performance_metrics(self):
        """Update performance tracking metrics"""
        import time
        current_time = time.time()
        
        # Calculate frame time
        if hasattr(self, '_last_frame_time'):
            frame_time = current_time - self._last_frame_time
            self.frame_time_samples.append(frame_time)
            
            # Keep only recent samples
            if len(self.frame_time_samples) > self.max_frame_time_samples:
                self.frame_time_samples.pop(0)
        
        self._last_frame_time = current_time
        
        # Update FPS counter
        self.fps_counter += 1
        self.fps_timer += 1
        
        # Calculate FPS every second
        if self.fps_timer >= 60:  # 60 frames at 60 FPS = 1 second
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.fps_timer = 0
            
            # Update performance optimizer with current metrics
            total_entities = len(self.enemies) + len(self.towers) + len(self.projectiles)
            self.performance_optimizer.update_performance_metrics(self.current_fps, total_entities)
    
    def get_performance_info(self) -> dict:
        """Get performance information for display"""
        avg_frame_time = 0
        if self.frame_time_samples:
            avg_frame_time = sum(self.frame_time_samples) / len(self.frame_time_samples)
        
        # Get optimization stats
        optimization_stats = self.performance_optimizer.get_optimization_stats()
        
        return {
            'fps': self.current_fps,
            'avg_frame_time_ms': avg_frame_time * 1000,  # Convert to milliseconds
            'entity_counts': {
                'enemies': len(self.enemies),
                'towers': len(self.towers),
                'projectiles': len(self.projectiles)
            },
            'optimization': {
                'level': self.performance_optimizer.optimization_level,
                'object_pooling': self.performance_optimizer.enable_object_pooling,
                'spatial_partitioning': self.performance_optimizer.enable_spatial_partitioning,
                'batch_processing': self.performance_optimizer.enable_batch_processing,
                'adaptive_updates': self.performance_optimizer.enable_adaptive_updates
            },
            'pool_stats': optimization_stats.get('pool_stats', {}),
            'spatial_grid_cells': optimization_stats.get('spatial_grid_cells', 0)
        }
    
    def get_tower_diversity_count(self) -> int:
        """Get the number of different tower types built"""
        tower_types_built = set()
        for tower in self.towers:
            tower_types_built.add(tower.tower_type)
        return len(tower_types_built)
    
    def get_adaptive_performance_data(self) -> dict:
        """Get performance data for the adaptive config generator"""
        # Calculate tower diversity
        towers_built_count = {}
        for tower in self.towers:
            tower_type = tower.tower_type
            towers_built_count[tower_type] = towers_built_count.get(tower_type, 0) + 1
        
        return {
            'win_flag': self.victory,
            'lives_remaining': self.lives,
            'starting_lives': self.starting_lives,
            'towers_built': towers_built_count,
            'tower_diversity': self.get_tower_diversity_count()
        }
    
    def _extract_enemy_types_from_config(self, config: dict) -> list:
        """Extract actual enemy types used from the configuration"""
        enemy_types = set()
        
        # Method 1: Extract from wave_compositions (most common)
        wave_config = config.get('wave_config', {})
        wave_compositions = wave_config.get('wave_compositions', {})
        
        for wave_range, compositions in wave_compositions.items():
            if isinstance(compositions, list):
                for composition in compositions:
                    if isinstance(composition, list) and len(composition) >= 1:
                        enemy_type = composition[0]  # First element is enemy type
                        if isinstance(enemy_type, str) and enemy_type.endswith('Enemy'):
                            enemy_types.add(enemy_type)
        
        # Method 2: Extract from spawn_config if it contains enemy-specific configs
        spawn_config = wave_config.get('spawn_config', {})
        for key in spawn_config.keys():
            if isinstance(key, str) and key.endswith('Enemy'):
                enemy_types.add(key)
        
        # Method 3: Extract from boss_waves
        boss_waves = wave_config.get('boss_waves', {})
        for wave_num, boss_config in boss_waves.items():
            if isinstance(boss_config, dict):
                boss_type = boss_config.get('boss_type') or boss_config.get('enemy_type')
                if boss_type and isinstance(boss_type, str):
                    enemy_types.add(boss_type)
            elif isinstance(boss_config, str):
                enemy_types.add(boss_config)
        
        # Method 4: Look for enemy scaling configs (enemy-specific scaling)
        enemy_scaling = wave_config.get('enemy_scaling', {})
        if isinstance(enemy_scaling, dict):
            for key in enemy_scaling.keys():
                if isinstance(key, str) and key.endswith('Enemy'):
                    enemy_types.add(key)
        
        # Convert to sorted list for consistent output
        result = sorted(list(enemy_types))
        
        # If no enemy types found, provide basic fallback
        if not result:
            # Look for any configuration that might indicate enemy types
            if 'BasicEnemy' in str(config):
                result = ['BasicEnemy']
            else:
                result = ['Unknown']
        
        return result

    def calculate_rock_removal_cost(self) -> int:
        """Calculate the cost to remove rocks based on AI-determined difficulty and current wave"""
        # Get difficulty from the config using comprehensive checking
        difficulty_level = 50  # Default difficulty
        
        if hasattr(self, 'full_config'):
            config = self.full_config
            
            # Check multiple locations for difficulty (same logic as game_config.py)
            if '_generation_metadata' in config:
                difficulty_level = config['_generation_metadata'].get('difficulty', 50)
            elif '_adaptive_metadata' in config:
                difficulty_level = config['_adaptive_metadata']['ai_adjustments']['difficulty_adjustment'].get('new_difficulty', 50)
            elif 'calculated_difficulty' in config:
                # Check if it's a dict with 'score' field (like tower_defense_game.json)
                calc_diff = config.get('calculated_difficulty')
                if isinstance(calc_diff, dict):
                    difficulty_level = calc_diff.get('score', 50)
                else:
                    difficulty_level = calc_diff
            elif 'game_config' in config and 'difficulty' in config['game_config']:
                # Check for difficulty in game_config section
                game_config = config['game_config']
                difficulty_value = game_config.get('difficulty')
                
                # Convert difficulty level names to scores
                if isinstance(difficulty_value, str):
                    difficulty_map = {
                        'test': 1,
                        'tutorial': 10,
                        'easy': 20,
                        'casual': 25,
                        'normal': 50,
                        'medium': 60,
                        'hard': 70,
                        'very_hard': 85,
                        'nightmare': 95,
                        'impossible': 100
                    }
                    difficulty_level = difficulty_map.get(difficulty_value.lower(), 50)
                elif isinstance(difficulty_value, (int, float)):
                    difficulty_level = int(difficulty_value)
            elif 'difficulty' in config:
                # Check for difficulty at root level
                diff_data = config.get('difficulty')
                if isinstance(diff_data, dict):
                    # Check for score in difficulty object
                    if 'score' in diff_data:
                        difficulty_level = diff_data['score']
                    elif 'level' in diff_data:
                        level = diff_data['level']
                        if isinstance(level, str):
                            difficulty_map = {
                                'test': 1,
                                'tutorial': 10,
                                'easy': 20,
                                'casual': 25,
                                'normal': 50,
                                'medium': 60,
                                'hard': 70,
                                'very_hard': 85,
                                'nightmare': 95,
                                'impossible': 100
                            }
                            difficulty_level = difficulty_map.get(level.lower(), 50)
                        elif isinstance(level, (int, float)):
                            difficulty_level = int(level)
                elif isinstance(diff_data, (int, float)):
                    difficulty_level = int(diff_data)
                elif isinstance(diff_data, str):
                    difficulty_map = {
                        'test': 1,
                        'tutorial': 10,
                        'easy': 20,
                        'casual': 25,
                        'normal': 50,
                        'medium': 60,
                        'hard': 70,
                        'very_hard': 85,
                        'nightmare': 95,
                        'impossible': 100
                    }
                    difficulty_level = difficulty_map.get(diff_data.lower(), 50)
        
        # Get current wave number for scaling
        current_wave = self.wave_manager.get_wave_info().get('wave_number', 1)
        
        # Get AI-determined rock removal parameters if available
        ai_base_cost = None
        ai_scaling_factor = None
        if hasattr(self, 'full_config') and 'rock_removal_config' in self.full_config:
            rock_config = self.full_config['rock_removal_config']
            ai_base_cost = rock_config.get('base_cost')
            ai_scaling_factor = rock_config.get('wave_scaling_factor')
        
        # Use the map's calculation method with wave progression
        return self.map.calculate_rock_removal_cost(difficulty_level, current_wave, ai_base_cost, ai_scaling_factor)
    
    def attempt_rock_removal(self, pos: Tuple[int, int]) -> bool:
        """Try to remove a rock at the given position and apply rewards"""
        x, y = pos
        
        # Check if there's a rock at this position
        if self.map.has_rock_at_position(x, y):
            # Check if player can afford it
            if self.money >= self.rock_removal_cost:
                # Remove the rock and charge the player
                if self.map.remove_rock_at_position(x, y):
                    self.money -= self.rock_removal_cost
                    self.total_money_spent += self.rock_removal_cost
                    
                    # Track rock removal for rewards
                    self.rock_removal_rewards.increment_rock_removed(self.wave_manager, self.towers, self.enemies, self)
                    
                    return True
        return False
    
    def apply_boost_to_new_tower(self, tower):
        """Apply current tower boost to a newly created tower"""
        self.rock_removal_rewards.apply_boost_to_new_tower(tower)
    
    def apply_boost_to_new_enemy(self, enemy):
        """Apply current enemy boost to a newly spawned enemy"""
        self.rock_removal_rewards.apply_boost_to_new_enemy(enemy)
    
    def apply_super_speed(self, multiplier):
        """Apply super speed effect to the game"""
        # Store original game speed if not already stored
        if not hasattr(self, '_original_game_speed'):
            self._original_game_speed = self.game_speed
        self.game_speed = self._original_game_speed * multiplier
        print(f"Super speed applied: {multiplier}x speed")
    
    def remove_super_speed(self):
        """Remove super speed effect from the game"""
        if hasattr(self, '_original_game_speed'):
            self.game_speed = self._original_game_speed
            print("Super speed removed")
    
    def test_rock_removal_rewards(self):
        """Test rock removal rewards without actually removing rocks"""
        if not self.rock_removal_rewards.debug_mode:
            return
        
        print("🧪 Testing rock removal rewards...")
        print(self.rock_removal_rewards.get_debug_info())
        
        # Test applying rewards
        rewards_activated = self.rock_removal_rewards.check_wave_rewards(
            self.wave_manager, self.towers, self.enemies, self
        )
        
        print(f"🧪 Rewards activated: {rewards_activated}")
        
        # Add test message
        self.rock_removal_rewards.add_message("🧪 Test completed - check console for details", (255, 255, 0))
    
    def draw(self):
        """Draw everything"""
        # Update screenshake first
        self.update_screenshake()
        offset_x, offset_y = self.screenshake_offset
        
        # Create a temporary surface to draw everything on
        temp_surface = pygame.Surface((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        temp_surface.fill((0, 0, 0))
        
        # Draw map (includes background and path)
        mouse_pos = pygame.mouse.get_pos()
        removing_rocks = self.ui_manager.is_rock_removal_mode()
        can_afford_removal = self.money >= self.rock_removal_cost
        self.map.draw(
            temp_surface, 
            self.tower_manager.placing_tower, 
            mouse_pos, 
            self.towers,
            self.tower_manager.selected_tower_type,
            removing_rocks,
            can_afford_removal
        )
        
        # Draw game objects
        self.draw_game_objects(temp_surface)
        
        # Draw UI
        game_state = self.get_game_state()
        self.ui_manager.draw_complete_ui(temp_surface, game_state)
        
        # Draw upgrade UI
        self.upgrade_ui.draw_upgrade_panel(temp_surface, self.upgrade_system)
        
        # Draw rock removal effects
        self.rock_removal_rewards.draw_effects(temp_surface)
        
        # Draw global upgrade UI if open
        if self.global_upgrade_ui.is_open:
            self.global_upgrade_ui.draw(temp_surface)
        
        # Draw victory points screen
        if self.show_victory_points:
            self.global_upgrade_ui.draw_victory_screen(temp_surface, self.victory_difficulty, self.victory_points_earned)
        
        # Blit the temporary surface to the screen with screenshake offset
        self.screen.blit(temp_surface, (offset_x, offset_y))
        
        pygame.display.flip()
    
    def restart_game(self):
        """Restart the game to initial state - COMPLETE RESET"""
        
        # Reset core game state
        self.game_over = False
        self.victory = False
        self.paused = False
        self.money = self.game_config.get('starting_money', 20)  # Reset to config amount
        self.lives = self.game_config.get('starting_lives', 20)  # Reset to config amount
        
        # Reset performance tracking
        self.starting_money = self.money
        self.starting_lives = self.lives
        self.total_money_spent = 0
        self.total_money_earned = self.starting_money

        # Reset game timing and tower tracking
        import time
        self.game_start_time = time.time()
        self.tower_build_order = []
        
        # Reset game speed to normal
        self.game_speed = 1
        self.current_speed_index = 0
        
        # Reset UI state completely
        self.show_victory_screen = False
        self.show_game_over_screen = False
        self.show_wave_complete = False
        self.restart_requested = False
        self.exit_to_menu_requested = False
        self.completed_wave_number = 0
        self.wave_bonus = 0
        self.wave_complete_timer = 0
        
        # Clear all game objects
        self.enemies.clear()
        self.towers.clear()
        self.projectiles.clear()
        
        # Reset all managers to initial state
        self.wave_manager = WaveManager(self.map.get_path())
        self.wave_manager.set_sprite_manager(self.sprite_manager)
        self.wave_manager.reset_introductions()  # Reset enemy introductions
        
        self.tower_manager = TowerManager()
        self.tower_manager.set_current_wave(1)  # Reset to wave 1 costs
        self.tower_manager.reset_ui_state()     # Reset tower placement UI state
        
        # Reset upgrade system
        self.upgrade_system = TowerUpgradeSystem()
        
        # Recreate UI manager with new tower manager reference
        self.ui_manager = UIManager(self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.tower_manager)
        self.upgrade_ui.reset_ui_state()        # Reset upgrade panel completely
        
        # Reset global upgrade UI state
        self.global_upgrade_ui.close_menu()
        self.show_victory_points = False
        
        # Reset performance tracking
        self.fps_counter = 0
        self.fps_timer = 0
        self.current_fps = 60
        self.frame_time_samples.clear()
        
        # Reset rock removal cost
        self.rock_removal_cost = self.calculate_rock_removal_cost()
        
        # Reset rock removal rewards system
        self.rock_removal_rewards = RockRemovalRewards()
        self.rocks_removed_this_wave = 0
        self.total_rocks_removed = 0
        
        # Reset tower boost system
        self.tower_boost_active = False
        self.tower_boost_timer = 0
        self.tower_boost_duration = 600  # 10 seconds at 60 FPS
        self.tower_boost_multiplier = 1.5  # 50% damage increase
        
        # Reset enemy boost system
        self.enemy_boost_active = False
        self.enemy_boost_timer = 0
        self.enemy_boost_duration = 300  # 5 seconds at 60 FPS
        self.enemy_boost_multiplier = 1.3  # 30% speed increase
    
    def run(self):
        """Main game loop"""
        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.update_performance_metrics()
            self.clock.tick(self.FPS)
        
        # Only quit pygame and exit if not launched from menu
        # If launched from menu, just return to let launcher handle the rest
        if not self.launched_from_menu:
            pygame.quit()
            sys.exit()

    def trigger_screenshake(self, intensity=10, duration=20):
        """Trigger a screenshake effect with given intensity and duration (in frames)"""
        self.screenshake_intensity = max(self.screenshake_intensity, intensity)
        self.screenshake_timer = max(self.screenshake_timer, duration)

    def update_screenshake(self):
        """Update screenshake offset and decay the effect each frame"""
        if self.screenshake_timer > 0:
            import random
            shake_x = random.randint(-self.screenshake_intensity, self.screenshake_intensity)
            shake_y = random.randint(-self.screenshake_intensity, self.screenshake_intensity)
            self.screenshake_offset = (shake_x, shake_y)
            self.screenshake_timer -= 1
            # Gradually reduce intensity
            if self.screenshake_timer < 10:
                self.screenshake_intensity = max(0, self.screenshake_intensity - 1)
        else:
            self.screenshake_offset = (0, 0)

    def check_first_win_and_generate_level(self):
        """Check if this is the first time winning this level and generate a new level if so"""
        if not self.victory:
            return
        
        try:
            # Get current config path
            from config.game_config import get_current_config_path
            current_config_path = get_current_config_path()
            if not current_config_path:
                return
            
            # Load or create level completion tracker
            completion_tracker_path = os.path.join("config", "level_completion_tracker.json")
            completed_levels = {}
            
            if os.path.exists(completion_tracker_path):
                try:
                    with open(completion_tracker_path, 'r') as f:
                        completed_levels = json.load(f)
                except:
                    completed_levels = {}
            
            # Check if this level has been completed before
            level_id = os.path.basename(current_config_path)
            if level_id not in completed_levels:
                # First time winning this level!
                print(f"🎉 FIRST WIN on level: {level_id}")
                
                # Mark level as completed
                completed_levels[level_id] = {
                    'first_completion_date': datetime.now().isoformat(),
                    'completion_count': 1
                }
                
                # Save the completion tracker
                os.makedirs(os.path.dirname(completion_tracker_path), exist_ok=True)
                with open(completion_tracker_path, 'w') as f:
                    json.dump(completed_levels, f, indent=2)
                
                # Generate new level automatically
                self.generate_level_on_first_win()
                
            else:
                # Level already completed before, just increment counter
                completed_levels[level_id]['completion_count'] = completed_levels[level_id].get('completion_count', 1) + 1
                with open(completion_tracker_path, 'w') as f:
                    json.dump(completed_levels, f, indent=2)
                print(f"🔄 Level {level_id} completed again (total: {completed_levels[level_id]['completion_count']} times)")
                
        except Exception as e:
            print(f"Error in first-win check: {e}")
    
    def generate_level_on_first_win(self):
        """Generate a new level automatically after first win"""
        try:
            print("🤖 Generating new level after first win...")
            
            from ai.adaptive_config_generator import AdaptiveConfigGenerator
            
            # Create adaptive config generator
            generator = AdaptiveConfigGenerator(use_modular_ai=True, use_full_ai=False)
            
            # Try to generate a new level
            config = generator.generate_config_from_recent_games()
            
            if config:
                print("✅ New level generated successfully after first win!")
                print(f"   Level name: {config.get('level_name', 'Unknown')}")
                
                # Add victory message to UI (if available)
                if hasattr(self, 'ui_manager') and hasattr(self.ui_manager, 'add_message'):
                    self.ui_manager.add_message("🎉 New level unlocked!", (0, 255, 0))
                
            else:
                print("❌ Failed to generate new level after first win")
                
        except Exception as e:
            print(f"Error generating level on first win: {e}")
            import traceback
            traceback.print_exc()

    def save_performance_data(self):
        """Save performance data to ai/performance_data/ as 1.json-5.json (circular buffer), only on victory or defeat."""
        # Only save if game is over (victory or defeat)
        if not (self.victory or self.game_over):
            return

        # Prepare enhanced performance data
        towers_built = {}
        for tower in self.towers:
            ttype = getattr(tower, 'tower_type', 'unknown')
            towers_built[ttype] = towers_built.get(ttype, 0) + 1

        tower_diversity = len(towers_built)

        # Find most built tower type
        most_built_tower_type = None
        if towers_built:
            most_built_tower_type = max(towers_built.items(), key=lambda x: x[1])[0]

        # Calculate economic metrics
        economic_efficiency = 0.0
        if self.total_money_spent > 0:
            economic_efficiency = self.total_money_earned / self.total_money_spent

        # Calculate resource management score (0-100)
        resource_management_score = 0.0
        if self.starting_money > 0:
            # Score based on money utilization and survival
            money_utilization = min(1.0, self.total_money_spent / (self.total_money_earned + 1))
            survival_rate = self.lives / self.starting_lives
            resource_management_score = (money_utilization * 0.6 + survival_rate * 0.4) * 100

        # Extract difficulty context from current config
        config_difficulty_score = None
        config_difficulty_description = None
        config_difficulty_components = None
        level_metadata = None

        if hasattr(self, 'full_config') and self.full_config:
            calc_diff = self.full_config.get('calculated_difficulty', {})
            if calc_diff:
                config_difficulty_score = calc_diff.get('score')
                config_difficulty_description = calc_diff.get('description')
                config_difficulty_components = calc_diff.get('formula_components', {})

            # Also extract level metadata for additional context
            level_metadata = self.full_config.get('level_metadata', {})

        # Calculate wave progression
        total_waves = self.full_config.get('wave_config', {}).get('total_waves', 80)
        waves_completed = getattr(self, 'completed_wave_number', 1)
        progression_percentage = (waves_completed / total_waves) * 100 if total_waves > 0 else 0.0

        # Calculate actual game duration
        import time
        actual_duration = time.time() - getattr(self, 'game_start_time', time.time())

        # Calculate average FPS
        avg_fps = getattr(self, 'current_fps', 60.0)

        perf = PerformanceData(
            win_flag=self.victory,
            lives_remaining=self.lives,
            starting_lives=self.starting_lives,
            towers_built=towers_built,
            tower_diversity=tower_diversity,
            wave_reached=waves_completed,
            final_wave=waves_completed,
            previous_config=None,
            previous_config_details=None,

            # Enhanced performance tracking
            starting_money=self.starting_money,
            money_remaining=self.money,
            total_money_spent=getattr(self, 'total_money_spent', 0),
            total_money_earned=getattr(self, 'total_money_earned', 0),
            game_duration_seconds=actual_duration,
            average_fps=avg_fps,

            # Strategic decision tracking
            most_built_tower_type=most_built_tower_type,
            tower_build_order=getattr(self, 'tower_build_order', []),
            economic_efficiency=economic_efficiency,
            resource_management_score=resource_management_score,

            # Difficulty context from config
            config_difficulty_score=config_difficulty_score,
            config_difficulty_description=config_difficulty_description,
            config_difficulty_components=config_difficulty_components,
            level_metadata=level_metadata,

            # Wave progression details
            waves_completed_successfully=waves_completed,
            total_waves_in_config=total_waves,
            progression_percentage=progression_percentage
        )
        data = perf.to_dict()
        # Add config file path for loader
        data['config_file_path'] = getattr(self, 'config_file_path', 'tower_defense_game.json')

        # Determine next file in circular buffer (1.json-5.json)
        perf_dir = os.path.join(os.path.dirname(__file__), 'ai', 'performance_data')
        os.makedirs(perf_dir, exist_ok=True)
        # Find the oldest or first available slot
        slots = [os.path.join(perf_dir, f'{i}.json') for i in range(1, 6)]
        slot_times = [(f, os.path.getmtime(f)) for f in slots if os.path.exists(f)]
        if len(slot_times) < 5:
            # Use first available slot
            for f in slots:
                if not os.path.exists(f):
                    target_file = f
                    break
        else:
            # Overwrite the oldest
            target_file = min(slot_times, key=lambda x: x[1])[0]
        # Write the performance file
        with open(target_file, 'w') as f:
            json.dump(data, f, indent=2)
        # Write completion marker for launcher
        marker_path = os.path.join(perf_dir, 'last_completion.txt')
        with open(marker_path, 'w') as f:
            f.write(f'{datetime.utcnow().isoformat()}Z\n{os.path.basename(target_file)}\n')
        print(f'Performance data saved to {target_file}')

    def activate_health_immunity(self, duration: int):
        """Activate health immunity for the player"""
        self.health_immunity_active = True
        self.health_immunity_timer = duration
    
    def remove_health_immunity(self):
        """Remove health immunity from the player"""
        self.health_immunity_active = False
        self.health_immunity_timer = 0

if __name__ == "__main__":
    game = Game()
    game.run() 